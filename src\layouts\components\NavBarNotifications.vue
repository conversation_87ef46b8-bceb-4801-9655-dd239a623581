<script lang="ts" setup>
import { useNotificationsStore } from '@/@core/stores/notifications'

// 使用全局通知 store
const notificationsStore = useNotificationsStore()

// 从 store 中获取响应式数据
const { notifications } = storeToRefs(notificationsStore)

// 移除/忽略通知
const removeNotification = async (notificationId: number) => {
  await notificationsStore.ignoreEvent(notificationId)
}

// 标记为已读
const markRead = async (notificationId: number) => {
  await notificationsStore.markEventAsRead(notificationId)
}

// 点击通知时标记为已读
const handleNotificationClick = async (notification: any) => {
  await notificationsStore.markEventAsRead(notification.event_id)
}

// 组件挂载时启动自动更新
onMounted(() => {
  notificationsStore.startAutoUpdate()
})

// 组件卸载时停止自动更新
onUnmounted(() => {
  notificationsStore.stopAutoUpdate()
})
</script>

<template>
  <Notifications
    :notifications="notifications"
    @ignore="removeNotification"
    @read="markRead"
    @click:notification="handleNotificationClick"
  />
</template>
