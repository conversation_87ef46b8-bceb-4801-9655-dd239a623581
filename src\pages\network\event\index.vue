<script lang="ts" setup>
import moment from 'moment/moment'
import { computed, reactive, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
import { useTheme } from 'vuetify'
import { DATE_FILTER_OPTIONS, NETWORK_EVENT_LEVEL } from '@/utils/constants'
import { useNotificationsStore } from '@/@core/stores/notifications'

const { t } = useI18n()
const vuetifyTheme = useTheme()

// 使用全局通知 store
const notificationsStore = useNotificationsStore()
const { notifications } = storeToRefs(notificationsStore)

// 变量提前声明
const timeList = ref([])
const page = ref(1)
const itemsPerPage = ref(10)
const currentEventType = ref(-1)

const totalList = ref([])
const hightList = ref([])
const lowList = ref([])
const mediumList = ref([])
const criticalList = ref([])

const balanceChartConfig = computed(() => {
  return {
    chart: {
      type: 'line',
      parentHeightOffset: 0,
      zoom: { enabled: false },
      toolbar: { show: false },
    },
    dataLabels: { enabled: false },
    colors: [
      vuetifyTheme.current.value.colors.error,
      vuetifyTheme.current.value.colors.warning,
      vuetifyTheme.current.value.colors.info,
      vuetifyTheme.current.value.colors.primary,
    ],
    stroke: {
      curve: 'smooth',
      width: 1.5,
      lineCap: 'round',
    },
    legend: {
      show: false,
    },
    tooltip: {
      custom({ dataPointIndex, series }: any) {
        return `
              <div class="chart-tooltip">
                <p class="chart-tooltip-title mb-2">${t('NetworkEvent.Title')}</p>
                <p class="chart-tooltip-label">${t('Time')}</p>
                <p class="chart-tooltip-value mb-2">${new Date().toDateString()}</p>
                <p class="chart-tooltip-label">${t('NetworkEvent.SevereEvents')}</p>
                <p class="chart-tooltip-value mb-2 text-error">${series[0][dataPointIndex]}</p>
                <p class="chart-tooltip-label">${t('NetworkEvent.NormalEvents')}</p>
                <p class="chart-tooltip-value mb-2 text-warning">${series[1][dataPointIndex]}</p>
                <p class="chart-tooltip-label">${t('NetworkEvent.MinorEvents')}</p>
                <p class="chart-tooltip-value mb-2 text-info">${series[2][dataPointIndex]}</p>
                <p class="chart-tooltip-label">${t('NetworkEvent.NotificationEvents')}</p>
                <p class="chart-tooltip-value mb-2 text-primary">${series[3][dataPointIndex]}</p>
              </div>`
      },
    },
    grid: {
      padding: { top: -10 },
      strokeDashArray: 4,
      xaxis: {
        lines: { show: true },
      },
    },
    markers: {
      size: 0,
      strokeWidth: 8,
      strokeOpacity: 1,
      strokeColors: ['#fff', '#fff', '#fff', '#fff', '#fff'],
      colors: [
        vuetifyTheme.current.value.colors.error,
        vuetifyTheme.current.value.colors.warning,
        vuetifyTheme.current.value.colors.info,
        vuetifyTheme.current.value.colors.primary,
      ],
      hover: {
        size: 8,
      },
    },
    xaxis: {
      axisBorder: { show: false },
      crosshairs: {
        stroke: {
          show: true,
          width: 1,
          color: '#999CA6',
          dashArray: 4,
        },
      },
      categories: timeList.value,
    },
  }
})

const series = computed(() => [
  {
    name: 'error',
    data: criticalList.value,
  },
  {
    name: 'warning',
    data: hightList.value,
  },
  {
    name: 'primary',
    data: mediumList.value,
  },
  {
    name: 'info',
    data: lowList.value,
  },
])

const date = ref(0)

interface AlarmEvent {
  event_name: string
  event_id: string
  type: string
  severity: string
  timestamp: string
  status: string
  mac: string
  ip: string
  sn: string
  description: string
  user_name: string
}

// 使用全局 store 中的事件数据，而不是本地状态
const eventsData = computed(() => notifications.value as AlarmEvent[])

const headers = [
  { title: t('NetworkEvent.EventLevel'), key: 'severity', sortable: false },
  { title: t('NetworkEvent.EventType'), key: 'type', sortable: false },
  { title: t('NetworkEvent.EventName'), key: 'event_name', sortable: false },
  { title: t('NetworkEvent.EventCount'), key: 'num', sortable: false },
  { title: t('NetworkEvent.AffectedDeviceCount'), key: 'deviceNum', sortable: false },
  { title: t('NetworkEvent.AffectedTerminalCount'), key: 'client_users', sortable: false },
  { title: t('NetworkEvent.EventTime'), key: 'timestamp', sortable: false },
  { title: t('NetworkEvent.Details'), key: 'detail', sortable: false },
]

const eventClass: Record<number, string> = {
  0: 'default',
  1: 'info',
  2: 'warning',
  3: 'error',
}

// 严重程度映射关系：NETWORK_EVENT_LEVEL value -> severity string
const severityMap = {
  0: 'Minor', // notification -> Minor
  1: 'Medium', // minor -> Medium
  2: 'High', // normal -> High
  3: 'Critical', // severe -> Critical
}

const eventTypesFilter = computed(() => {
  const arr = [
    {
      title: 'All',
      value: -1,
    },
    ...NETWORK_EVENT_LEVEL,
  ]

  return arr.map(item => ({
    ...item,
    title: t(item.title),
  }))
})

const DATE_FILTER_OPTIONS_LIST = computed(() => {
  return DATE_FILTER_OPTIONS.map(item => ({
    ...item,
    title: t(item.title),
  }))
})

const eventList = computed(() => {
  let list = eventsData.value

  // 先按严重程度过滤
  if (currentEventType.value !== -1) {
    const targetSeverity = severityMap[currentEventType.value]
    if (targetSeverity)
      list = list.filter(item => item.severity === targetSeverity)
  }

  // 再按分页切片
  const start = (page.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value

  return list.slice(start, end)
})

const totalProduct = computed(() => {
  if (currentEventType.value === -1)
    return eventsData.value.length

  const targetSeverity = severityMap[currentEventType.value]
  if (targetSeverity)
    return eventsData.value.filter(item => item.severity === targetSeverity).length || 0

  return eventsData.value.length
})

const deviceEventTableHeaders = [
  {
    title: t('NetworkEvent.DeviceName'),
    key: 'name',
    sortable: false,
  },
  { title: t('NetworkEvent.EventCount'), key: 'no', sortable: false },
  { title: 'Mac', key: 'mac', sortable: false },
  { title: 'IP', key: 'ip', sortable: false },
]

const terminalEventTableHeaders = [{
  title: `${t('Terminal')}[MAC]`,
  key: 'name',
}, { title: t('NetworkEvent.EventCount'), key: 'no' }]

const networkEventNum = reactive({
  all: 0,
  error: 0,
  simple: 0,
  info: 0,
  primary: 0,
})

const deviceEventList = ref([])

const clientEventList = ref([])

const getEventStatistic = () => {
  $api('', { requestType: 523 }).then(res => {
    if (res.err_code === 0) {
      const { Critical, High, Medium, Minor } = res.info.event_summary

      networkEventNum.all = Critical + High + Medium + Minor
      networkEventNum.error = Critical
      networkEventNum.simple = High
      networkEventNum.info = Medium
      networkEventNum.primary = Minor
    }
  })
}

const getChartDataList = () => {
  $api('', { requestType: 539 }).then(res => {
    if (res.err_code === 0) {
      const list = res.info.detail

      // 倒序
      list.reverse()
      timeList.value = list.map(item => item.date.split(' ')[1])
      hightList.value = list.map(item => item.High)
      mediumList.value = list.map(item => item.Medium)
      lowList.value = list.map(item => item.Minor)
      criticalList.value = list.map(item => item.Critical)
      totalList.value = list.map(item => 0 || item.Minor)
    }
  })
}

const getEventList = async () => {
  // 使用全局 store 获取事件数据
  await notificationsStore.fetchEventList()

  // 处理设备事件列表（保持原有逻辑）
  const list = notifications.value

  deviceEventList.value = list
    .filter(event => event.device_type === 'AC' || event.device_type === 'AP')
    .reduce((acc: any[], event: any) => {
      const existingEntry = acc.find((entry: any) => entry.mac === event.mac)
      if (existingEntry) {
        existingEntry.count++
      }
      else {
        acc.push({
          mac: event.mac,
          ip: event.ip,
          count: 1,
          device_type: event.device_type,
          user_name: event.user_name,
        })
      }

      return acc
    }, [])

  clientEventList.value = list
    .filter(event => event.device_type !== 'AC' && event.device_type !== 'AP')
    .reduce((acc: any[], event: any) => {
      const existingEntry = acc.find((entry: any) => entry.mac === event.mac)
      if (existingEntry) {
        existingEntry.count++
      }
      else {
        acc.push({
          mac: event.mac,
          count: 1,
          device_type: event.device_type,
          user_name: event.user_name,
        })
      }

      return acc
    }, [])

  // 汇总数据 device_type  等于AC,AP的
  // 同一个mac的数据汇总组成新的对象数组，属性包括当前mac一共出现几次，以及当前mac的device_type  和user_name  组成新数组
}

const router = useRouter()

const viewDetail = (item: any) => {
  router.push({
    name: 'network-event-detail',
    query: {
      event_id: item.event_id,
    },
  })
}

onMounted(async () => {
  getEventStatistic()
  getEventList()
  getChartDataList()

  // 启动全局事件列表的自动更新
  notificationsStore.startAutoUpdate()
})

onUnmounted(() => {
  // 页面卸载时停止自动更新
  notificationsStore.stopAutoUpdate()
})

const drawer = ref(false)

const eventDetail = ref({} as AlarmEvent)

const deviceInfo = ref({
  model: '',
  version: '',
  ip: '',
  mac: '',
} as { model: string; version: string; ip: string; mac: string })

const formatTime = (time: string) => {
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

const openDrawer = (item: any) => {
  $api('', {
    requestType: 530,
    data: { mac: item.mac },
  }).then((res => {
    if (res.err_code === 0)
      deviceInfo.value = res.info
  }))
  eventDetail.value = item
  drawer.value = true
}

const getEventIndex = (item: any): number => {
  const str = item.severity

  // Critical/High/Medium/Minor
  if (str == 'Critical')
    return 3
  else if (str == 'High')
    return 2
  else if (str == 'Medium')
    return 1
  else if (str == 'Minor')
    return 0

  return 0 // 默认返回 0，防止 undefined
}

const getEventName = (item: any) => {
  const index = getEventIndex(item)
  if (index >= 0 && index < NETWORK_EVENT_LEVEL.length)
    return t(NETWORK_EVENT_LEVEL[index].title)

  return t('Unknown')
}

const markResolved = (item: any) => {
  $api('', {
    requestType: 524,
    data: {
      event_id: item.event_id,
      status: '2',
    },
  }).then((res => {
    if (res.err_code === 0)
      getEventList()

    drawer.value = false
  }))
}

// 监听事件类型变化，重置分页到第一页
watch(currentEventType, () => {
  page.value = 1
})
</script>

<template>
  <div class="network-event">
    <VCard class="mb-6">
      <template #title>
        <div class="d-flex align-start justify-space-between">
          <div class="mr-2 text-h5">
            {{ t('NetworkEvent.Title') }}
          </div>
        </div>
      </template>
      <VCardText>
        <div class="status-statistic mb-4">
          <div class="status-statistic-item">
            <div class="label mb-2">
              {{ t('NetworkEvent.TotalEvents') }}
            </div>
            <div class="num">
              {{ networkEventNum.all }}
            </div>
          </div>
          <div class="status-statistic-item error">
            <div class="label mb-2">
              {{ t('NetworkEvent.Severe') }}
            </div>
            <div class="num text-error">
              {{ networkEventNum.error }}
            </div>
          </div>
          <div class="status-statistic-item warning">
            <div class="label mb-2">
              {{ t('NetworkEvent.Normal') }}
            </div>
            <div class="num text-warning">
              {{ networkEventNum.simple }}
            </div>
          </div>
          <div class="status-statistic-item info">
            <div class="label mb-2">
              {{ t('NetworkEvent.Minor') }}
            </div>
            <div class="num text-info">
              {{ networkEventNum.info }}
            </div>
          </div>
          <div class="status-statistic-item secondary">
            <div class="label mb-2">
              {{ t('NetworkEvent.Notification') }}
            </div>
            <div class="num text-primary">
              {{ networkEventNum.primary }}
            </div>
          </div>
        </div>
        <VueApexCharts
          type="line"
          height="250"
          :options="balanceChartConfig"
          :series="series"
        />
      </VCardText>
    </VCard>

    <VRow class="mb-6">
      <VCol cols="12">
        <VCard>
          <template #title>
            <div class="mr-2 text-h5">
              {{ t('NetworkEvent.DevicesWithMoreEvents') }}
            </div>
          </template>
          <VDivider />
          <div
            style="block-size: 300px;"
            class="overflow-y-scroll"
          >
            <VDataTableServer
              :items="deviceEventList"
              :headers="deviceEventTableHeaders"
              :items-length="totalProduct"
              hide-default-footer
              :no-data-text=" t('NoData')"
              disable-sort
            >
              <template #item.name="{ item }">
                <div>
                  <div class="text-button">
                    {{ item.user_name }}
                  </div>
                  <div class="text-subtitle-2">
                    {{ item.device_type }}
                  </div>
                </div>
              </template>
              <template #item.mac="{ item }">
                <span class="text-button">{{ item.mac }}</span>
              </template>
              <template #item.no="{ item }">
                <span class="text-button">{{ item.count }}</span>
              </template>
            </VDataTableServer>
          </div>
        </VCard>
      </VCol>
      <!--      <VCol cols="6"> -->
      <!--        <VCard> -->
      <!--          <template #title> -->
      <!--            <div class="mr-2 text-h5"> -->
      <!--              {{ t('NetworkEvent.TerminalsWithMoreEvents') }} -->
      <!--            </div> -->
      <!--          </template> -->
      <!--          <VDivider /> -->
      <!--          <div -->
      <!--            style="block-size: 300px;" -->
      <!--            class="overflow-y-scroll" -->
      <!--          > -->
      <!--            <VDataTableServer -->
      <!--              :items="clientEventList" -->
      <!--              :headers="terminalEventTableHeaders" -->
      <!--              :items-length="totalProduct" -->
      <!--              hide-default-footer -->
      <!--              :no-data-text=" t('NoData')" -->
      <!--            > -->
      <!--              <template #item.name="{ item }"> -->
      <!--                <div> -->
      <!--                  <div class="text-button"> -->
      <!--                    {{ item.user_name }} -->
      <!--                  </div> -->
      <!--                  <div class="text-overline"> -->
      <!--                    {{ item.mac }} -->
      <!--                  </div> -->
      <!--                </div> -->
      <!--              </template> -->
      <!--              <template #item.no="{ item }"> -->
      <!--                <span class="text-button">{{ item.count }}</span> -->
      <!--              </template> -->
      <!--            </VDataTableServer> -->
      <!--          </div> -->
      <!--        </VCard> -->
      <!--      </VCol> -->
    </VRow>

    <VCard>
      <template #title>
        <div class="d-flex align-start">
          <div class="mr-6">
            {{ t('NetworkEvent.EventList') }}
          </div>
          <BtnGroupSelector
            v-model:value="currentEventType"
            class="mr-6"
            :options="eventTypesFilter"
            item-title="title"
            item-value="value"
          />
        </div>
      </template>
      <VDivider />
      <VDataTableServer
        :items="eventList"
        :headers="headers"
        :items-length="totalProduct"
        :no-data-text="t('NoData')"
        disable-sort
      >
        <template #item.num="{ item }">
          0
        </template>
        <template #item.deviceNum="{ item }">
          0
        </template>
        <template #item.timestamp="{ item }">
          {{ formatTime(item.timestamp) }}
        </template>
        <template #item.type="{ item }">
          <div>
            <div v-if="item.type === '0'">
              {{ t('NetworkEvent.DeviceTypes.AccessType') }}
            </div>
            <div v-if="item.type === '1'">
              {{ t('NetworkEvent.DeviceTypes.PerformanceType') }}
            </div>
          </div>
        </template>
        <template #item.event_name="{ item }">
          <div
            class="text-primary"
            @click="viewDetail(item)"
          >
            <div v-if="item.event_name === '0'">
              {{ t('NetworkEvent.EventNames.APOnline') }}
            </div>
            <div v-if="item.event_name === '1'">
              {{ t('NetworkEvent.EventNames.APOffline') }}
            </div>
            <div v-if="item.event_name === '3'">
              {{ t('NetworkEvent.EventNames.APHighTemp') }}
            </div>
          </div>
        </template>
        <template #item.severity="{ item }">
          <VChip
            variant="outlined"
            :color="eventClass[getEventIndex(item)]"
            c
            @click="viewDetail(item)"
          >
            {{ getEventName(item) }}
          </VChip>
        </template>
        <template #item.detail="{ item }">
          <VBtn
            variant="text"
            color="primary"
            @click="openDrawer(item)"
          >
            {{ t('NetworkEvent.ViewDetails') }}
          </VBtn>
        </template>
        <template #bottom>
          <TablePagination
            v-model:page="page"
            v-model:items-per-page="itemsPerPage"
            :total-items="totalProduct"
            show-meta
          />
        </template>
      </VDataTableServer>
    </VCard>
    <!-- 事件详情 -->
    <VNavigationDrawer
      v-model="drawer"
      location="right"
      temporary
      width="560"
      style="block-size: 100%;"
    >
      <div
        class="drawer-content"
        style="display: flex; flex-direction: column; block-size: 100%; min-block-size: 0;"
      >
        <!-- 顶部固定 -->
        <div class="flex-shrink-0 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ t('NetworkEvent.EventDetails') }}
          </div>
          <VBtn
            icon
            variant="text"
            color="medium-emphasis"
            size="small"
            @click="drawer = false"
          >
            <VIcon
              icon="tabler-x"
              color="high-emphasis"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <!-- 中间滚动区域外层flex包裹 -->
        <div style="display: flex; flex: 1 1 0; flex-direction: column; min-block-size: 0;">
          <PerfectScrollbar
            class="pa-4 hide-scrollbar"
            tag="ul"
            :options="{ wheelPropagation: false }"
            style="flex: 1 1 0; min-block-size: 0;"
          >
            <div class="text-h5 mb-4 d-flex">
              {{ deviceInfo.model }}
              <div class="ml-2 mr-2">
                :
              </div>
              <div v-if="eventDetail.event_name === '0'">
                {{ t('NetworkEvent.EventNames.APOnline') }}
              </div>
              <div v-if="eventDetail.event_name === '1'">
                {{ t('NetworkEvent.EventNames.APOffline') }}
              </div>
              <div v-if="eventDetail.event_name === '3'">
                {{ t('NetworkEvent.EventNames.APHighTemp') }}
              </div>
            </div>
            <div class="bg-grey-light pa-4 mb-4 rounded">
              <div class="text-button mb-2">
                {{ t('NetworkEvent.BasicInfo') }}
              </div>
              <VRow>
                <VCol>
                  <div class="label">
                    {{ t('NetworkEvent.Type') }}
                  </div>
                  <div class="value">
                    <div v-if="eventDetail.type === '0'">
                      {{ t('NetworkEvent.DeviceTypes.AccessType') }}
                    </div>
                    <div v-if="eventDetail.type === '1'">
                      {{ t('NetworkEvent.DeviceTypes.PerformanceType') }}
                    </div>
                  </div>
                </VCol>
                <VCol>
                  <div class="label">
                    {{ t('NetworkEvent.Level') }}
                  </div>
                  <div class="value">
                    <VChip
                      variant="outlined"
                      :color="eventClass[getEventIndex(eventDetail)]"
                    >
                      {{ getEventName(eventDetail) }}
                    </VChip>
                  </div>
                </VCol>
                <VCol>
                  <div class="label">
                    {{ t('NetworkEvent.OccurrenceTime') }}
                  </div>
                  <div class="value">
                    {{ formatTime(eventDetail.timestamp) }}
                  </div>
                </VCol>
              </VRow>
              <VRow>
                <VCol>
                  <div class="label">
                    {{ t('NetworkEvent.EventDescription') }}
                  </div>
                  <div v-if="eventDetail.event_name === '0'">
                    {{ eventDetail.description || t('NetworkEvent.EventNames.APOnline') }}
                  </div>
                  <div v-if="eventDetail.event_name === '1'">
                    {{ eventDetail.description || t('NetworkEvent.EventNames.APOffline') }}
                  </div>
                  <div v-if="eventDetail.event_name === '3'">
                    {{ eventDetail.description || t('NetworkEvent.EventNames.APHighTemp') }}
                  </div>
                </VCol>
              </VRow>
            </div>

            <div class="bg-grey-light pa-4 mb-4 rounded">
              <div class="text-button mb-2">
                {{ t('NetworkEvent.DeviceInfo') }}
              </div>
              <VRow>
                <VCol>
                  <div class="label">
                    {{ t('NetworkEvent.DeviceName') }}
                  </div>
                  <div class="value">
                    {{ eventDetail.user_name }}
                  </div>
                </VCol>
                <VCol>
                  <div class="label">
                    {{ t('NetworkEvent.Status') }}
                  </div>
                  <div
                    v-if="eventDetail.device_status === 'online'"
                    class="value text-success"
                  >
                    {{ t('NetworkEvent.Online') }}
                  </div>
                  <div
                    v-else
                    class="value text-error"
                  >
                    {{ t('NetworkEvent.Offline') }}
                  </div>
                </VCol>
                <VCol>
                  <div class="label">
                    {{ t('NetworkEvent.SoftwareVersion') }}
                  </div>
                  <div class="value">
                    {{ deviceInfo.version }}
                  </div>
                </VCol>
              </VRow>
              <VRow>
                <VCol>
                  <div class="label">
                    {{ t('NetworkEvent.IPAddress') }}
                  </div>
                  <div class="value">
                    {{ deviceInfo.ip }}
                  </div>
                </VCol>
                <VCol>
                  <div class="label">
                    {{ t('NetworkEvent.MACAddress') }}
                  </div>
                  <div class="value">
                    {{ deviceInfo.mac }}
                  </div>
                </VCol>
                <VCol />
              </VRow>
            </div>

            <div class="rounded pa-4 border border-sm">
              <div class="text-button mb-2">
                {{ t('NetworkEvent.MaintenanceSuggestions') }}
              </div>
              <div class="text-on-surface opacity-60 text-body-1">
                {{ t('NetworkEvent.MaintenanceDesc') }}
              </div>
            </div>
          </PerfectScrollbar>
        </div>

        <VDivider />
        <!-- 底部固定内容（原有内容不变） -->
        <div class="flex-shrink-0 pa-4 d-flex align-center justify-end ">
          <VBtn
            class="mr-4"
            color="secondary"
            variant="tonal"
            @click="drawer = false"
          >
            {{ t('NetworkEvent.Cancel') }}
          </VBtn>
          <VBtn
            v-if="eventDetail.status == '0'"
            color="primary"
            @click="markResolved(eventDetail)"
          >
            {{ t('NetworkEvent.MarkAsResolved') }}
          </VBtn>
        </div>
      </div>
    </VNavigationDrawer>
  </div>
</template>

<style lang="scss" scoped>
.network-event {
  .status-statistic {
    display: grid;
    column-gap: 10px;
    grid-template-columns: repeat(5, 1fr);

    &-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      background: rgba($color: var(--v-theme-on-surface), $alpha: 8%);
      block-size: 76px;
      inline-size: 100%;

      .label {
        color: rgba(var(--v-theme-on-surface), 0.9);
        font-size: 15px;
      }

      .num {
        color: rgb(var(--v-theme-on-surface), 0.9);
        font-size: 28px;
        font-weight: 500;
      }

      &.success {
        background: rgba($color: var(--v-theme-success), $alpha: 8%);
      }

      &.error {
        background: rgba($color: var(--v-theme-error), $alpha: 8%);
      }

      &.warning {
        background: rgba($color: var(--v-theme-warning), $alpha: 8%);
      }

      &.info {
        background: rgba($color: var(--v-theme-info), $alpha: 8%);
      }

      &.secondary {
        background: rgba($color: var(--v-theme-secondary), $alpha: 8%);
      }
    }
  }
}

.hide-scrollbar {
  // 隐藏滚动条（兼容主流浏览器）
  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none; // IE/Edge
  scrollbar-width: none; // Firefox
}
</style>

<style lang="scss">
.custom-pie-chart {
  .apexcharts-legend {
    &-series {
      border: 1px solid rgba($color: var(--v-theme-on-surface), $alpha: 12%);
      border-radius: 6px;
      padding-block: 3px;
      padding-inline: 15px;
    }
  }
}
</style>
