<template>
  <VContainer>
    <VCard>
      <VCardTitle>
        事件通知测试页面
      </VCardTitle>
      <VCardText>
        <VRow>
          <VCol cols="12" md="6">
            <VCard>
              <VCardTitle>全局事件状态</VCardTitle>
              <VCardText>
                <p><strong>总事件数:</strong> {{ notifications.length }}</p>
                <p><strong>未读事件数:</strong> {{ unreadCount }}</p>
                <p><strong>是否有未读:</strong> {{ hasUnreadNotifications ? '是' : '否' }}</p>
                <p><strong>最后更新时间:</strong> {{ lastUpdateTime ? formatTime(lastUpdateTime) : '未更新' }}</p>
                <p><strong>加载状态:</strong> {{ isLoading ? '加载中...' : '已完成' }}</p>
              </VCardText>
            </VCard>
          </VCol>
          
          <VCol cols="12" md="6">
            <VCard>
              <VCardTitle>操作控制</VCardTitle>
              <VCardText>
                <VBtn 
                  color="primary" 
                  @click="manualRefresh"
                  :loading="isLoading"
                  class="mr-2 mb-2"
                >
                  手动刷新
                </VBtn>
                <VBtn 
                  color="success" 
                  @click="startAutoUpdate"
                  class="mr-2 mb-2"
                >
                  启动自动更新
                </VBtn>
                <VBtn 
                  color="warning" 
                  @click="stopAutoUpdate"
                  class="mr-2 mb-2"
                >
                  停止自动更新
                </VBtn>
              </VCardText>
            </VCard>
          </VCol>
        </VRow>
        
        <VRow class="mt-4">
          <VCol cols="12">
            <VCard>
              <VCardTitle>事件列表</VCardTitle>
              <VCardText>
                <VDataTable
                  :headers="headers"
                  :items="notifications"
                  :items-per-page="10"
                  class="elevation-1"
                >
                  <template #item.isRead="{ item }">
                    <VChip
                      :color="item.isRead === 0 ? 'error' : 'success'"
                      size="small"
                    >
                      {{ item.isRead === 0 ? '未读' : '已读' }}
                    </VChip>
                  </template>
                  
                  <template #item.severity="{ item }">
                    <VChip
                      :color="getSeverityColor(item.severity)"
                      size="small"
                    >
                      {{ item.severity }}
                    </VChip>
                  </template>
                  
                  <template #item.timestamp="{ item }">
                    {{ formatTime(item.timestamp) }}
                  </template>
                  
                  <template #item.actions="{ item }">
                    <VBtn
                      v-if="item.isRead === 0"
                      size="small"
                      color="primary"
                      @click="markAsRead(item.event_id)"
                    >
                      标记已读
                    </VBtn>
                    <VBtn
                      v-if="item.status === 0"
                      size="small"
                      color="warning"
                      @click="ignoreEvent(item.event_id)"
                      class="ml-2"
                    >
                      忽略
                    </VBtn>
                  </template>
                </VDataTable>
              </VCardText>
            </VCard>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
  </VContainer>
</template>

<script lang="ts" setup>
import moment from 'moment'
import { useNotificationsStore } from '@/@core/stores/notifications'

// 使用全局通知 store
const notificationsStore = useNotificationsStore()
const { 
  notifications, 
  isLoading, 
  lastUpdateTime, 
  unreadCount, 
  hasUnreadNotifications 
} = storeToRefs(notificationsStore)

// 表格头部
const headers = [
  { title: '事件ID', key: 'event_id', sortable: true },
  { title: '事件名称', key: 'event_name', sortable: false },
  { title: '严重程度', key: 'severity', sortable: true },
  { title: '读取状态', key: 'isRead', sortable: true },
  { title: '时间', key: 'timestamp', sortable: true },
  { title: '操作', key: 'actions', sortable: false },
]

// 格式化时间
const formatTime = (time: string | Date) => {
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

// 获取严重程度颜色
const getSeverityColor = (severity: string) => {
  switch (severity) {
    case 'Critical': return 'error'
    case 'High': return 'warning'
    case 'Medium': return 'info'
    case 'Minor': return 'success'
    default: return 'default'
  }
}

// 手动刷新
const manualRefresh = () => {
  notificationsStore.fetchEventList()
}

// 启动自动更新
const startAutoUpdate = () => {
  notificationsStore.startAutoUpdate()
}

// 停止自动更新
const stopAutoUpdate = () => {
  notificationsStore.stopAutoUpdate()
}

// 标记为已读
const markAsRead = async (eventId: number) => {
  await notificationsStore.markEventAsRead(eventId)
}

// 忽略事件
const ignoreEvent = async (eventId: number) => {
  await notificationsStore.ignoreEvent(eventId)
}

// 页面挂载时启动自动更新
onMounted(() => {
  notificationsStore.startAutoUpdate()
})

// 页面卸载时停止自动更新
onUnmounted(() => {
  notificationsStore.stopAutoUpdate()
})
</script>
