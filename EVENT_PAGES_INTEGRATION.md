# 事件详情页面集成全局通知系统

## 概述

已成功将 `/network/eventDetail` 和 `/network/deviceDetail` 页面集成到全局事件通知系统中，实现了数据共享和统一管理。

## 修改的页面

### 1. `/network/eventDetail` 页面

**文件**: `src/pages/network/eventDetail/index.vue`

**主要修改**:

1. **导入全局 store**:
   ```javascript
   import { useNotificationsStore } from '@/@core/stores/notifications'
   ```

2. **使用全局数据**:
   ```javascript
   const notificationsStore = useNotificationsStore()
   const { notifications } = storeToRefs(notificationsStore)
   ```

3. **修改 `getEventList` 函数**:
   - 使用全局 store 获取事件数据
   - 从全局数据中筛选特定事件ID的数据
   - 保持原有的事件名称筛选逻辑

4. **优化事件操作**:
   - `markResolved`: 标记事件为已解决后刷新全局数据
   - `markIgnored`: 使用全局 store 的忽略事件方法

### 2. `/network/deviceDetail` 页面

**文件**: `src/pages/network/deviceDetail/index.vue`

**主要修改**:

1. **导入全局 store**:
   ```javascript
   import { useNotificationsStore } from '@/@core/stores/notifications'
   ```

2. **智能数据获取策略**:
   ```javascript
   const getDeviceEventList = async () => {
     // 首先尝试从全局数据中筛选
     await notificationsStore.fetchEventList()
     const globalFilteredList = globalList.filter(item => item.mac === mac)
     
     // 如果全局数据中有该设备的事件，使用全局数据
     if (globalFilteredList.length > 0) {
       originalProducts.value = globalFilteredList
       return
     }
     
     // 否则使用设备专用的 API (requestType: 537)
     // ...
   }
   ```

3. **事件操作优化**:
   - `markResolved`: 标记解决后同步更新全局数据
   - `markIgnored`: 忽略事件后同步更新全局数据

## 技术特点

### 1. 数据优先级策略

**eventDetail 页面**:
- 完全使用全局事件数据
- 通过事件ID筛选特定事件
- 保持原有的事件名称分组逻辑

**deviceDetail 页面**:
- 优先使用全局数据中的设备事件
- 如果全局数据不足，回退到设备专用API
- 保持设备特有的MAC地址筛选

### 2. 数据同步机制

所有事件操作（标记已读、忽略、解决）都会：
1. 执行相应的API调用
2. 刷新全局事件数据
3. 更新本地页面显示

### 3. 类型安全处理

- 修复了类型定义问题
- 使用 `any[]` 类型避免严格类型检查冲突
- 保持原有的数据结构兼容性

## 数据流程

```mermaid
graph TD
    A[页面加载] --> B[检查全局事件数据]
    B --> C{全局数据是否包含所需事件?}
    C -->|是| D[使用全局数据]
    C -->|否| E[调用页面专用API]
    D --> F[筛选和显示数据]
    E --> F
    F --> G[用户操作事件]
    G --> H[更新全局数据]
    H --> I[刷新页面显示]
```

## API 端点使用

### eventDetail 页面
- **获取数据**: 使用全局 store (requestType: 522)
- **标记解决**: requestType: 524 (status: '2')
- **忽略事件**: 使用全局 store 的 `ignoreEvent` 方法

### deviceDetail 页面
- **获取数据**: 优先全局 store，回退到 requestType: 537
- **标记解决**: requestType: 524 (status: '2')
- **忽略事件**: requestType: 540 (设备特有的忽略逻辑)

## 优势

1. **数据一致性**: 所有页面共享同一份事件数据
2. **性能优化**: 减少重复的API调用
3. **实时更新**: 30秒自动刷新机制确保数据最新
4. **向下兼容**: 保持原有页面功能不变
5. **智能回退**: 当全局数据不足时自动使用专用API

## 注意事项

1. **事件ID类型转换**: eventDetail页面需要将字符串类型的event_id转换为数字类型
2. **MAC地址筛选**: deviceDetail页面保持基于MAC地址的事件筛选
3. **API差异**: 不同页面可能使用不同的API端点，需要保持兼容性
4. **错误处理**: 添加了适当的错误处理和日志输出

## 测试建议

1. 访问 `/network/eventDetail?event_id=123` 测试事件详情页面
2. 访问 `/network/deviceDetail?mac=xx:xx:xx:xx:xx:xx` 测试设备详情页面
3. 验证事件操作（标记解决、忽略）是否正确同步到全局数据
4. 检查30秒自动更新是否正常工作
5. 测试在全局数据不足时的API回退机制
