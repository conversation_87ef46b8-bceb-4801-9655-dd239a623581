# 全局事件通知系统

## 概述

实现了一个全局的事件通知系统，支持30秒自动更新，事件列表存储在全局状态中，多个页面可以共享同一份数据。

## 主要功能

### 1. 全局状态管理
- 使用 Pinia store 管理事件通知数据
- 所有页面共享同一份事件数据
- 响应式数据更新，自动同步到所有使用的组件

### 2. 自动更新机制
- 30秒定时自动获取最新事件列表
- 可手动启动/停止自动更新
- 页面挂载时自动启动，卸载时自动停止

### 3. 事件操作
- 标记事件为已读
- 忽略事件
- 获取未读事件数量
- 按严重程度过滤事件

## 文件结构

```
src/
├── @core/stores/
│   └── notifications.ts          # 全局事件通知 store
├── layouts/components/
│   └── NavBarNotifications.vue   # 导航栏通知组件（已更新）
├── pages/
│   ├── network/event/index.vue   # 网络事件页面（已更新）
│   └── test-notifications.vue    # 测试页面
└── docs/
    └── notifications-store.md    # 本文档
```

## 核心代码

### Store 定义 (`src/@core/stores/notifications.ts`)

```typescript
export const useNotificationsStore = defineStore('notifications', () => {
  // 状态
  const notifications = ref<NotificationEvent[]>([])
  const isLoading = ref(false)
  const lastUpdateTime = ref<Date | null>(null)
  const updateTimer = ref<NodeJS.Timeout | null>(null)
  
  // 配置
  const UPDATE_INTERVAL = 30 * 1000 // 30秒

  // 主要方法
  const fetchEventList = async () => { /* 获取事件列表 */ }
  const markEventAsRead = async (eventId: number) => { /* 标记已读 */ }
  const ignoreEvent = async (eventId: number) => { /* 忽略事件 */ }
  const startAutoUpdate = () => { /* 启动自动更新 */ }
  const stopAutoUpdate = () => { /* 停止自动更新 */ }

  // 计算属性
  const unreadCount = computed(() => { /* 未读数量 */ })
  const hasUnreadNotifications = computed(() => { /* 是否有未读 */ })

  return {
    notifications, isLoading, lastUpdateTime,
    unreadCount, hasUnreadNotifications,
    fetchEventList, markEventAsRead, ignoreEvent,
    startAutoUpdate, stopAutoUpdate
  }
})
```

### 组件使用示例

```vue
<script setup>
import { useNotificationsStore } from '@/@core/stores/notifications'

const notificationsStore = useNotificationsStore()
const { notifications, unreadCount } = storeToRefs(notificationsStore)

onMounted(() => {
  notificationsStore.startAutoUpdate()
})

onUnmounted(() => {
  notificationsStore.stopAutoUpdate()
})
</script>
```

## API 接口

### 获取事件列表
- **接口**: `requestType: 522`
- **方法**: GET
- **返回**: `{ err_code: 0, info: { ap_events: [] } }`

### 标记事件已读
- **接口**: `requestType: 538`
- **方法**: POST
- **参数**: `{ event_id: number, isRead: '1' }`

### 忽略事件
- **接口**: `requestType: 524`
- **方法**: POST
- **参数**: `{ event_id: number, status: '1' }`

## 使用说明

### 1. 在组件中使用

```vue
<script setup>
import { useNotificationsStore } from '@/@core/stores/notifications'

const store = useNotificationsStore()
const { notifications, unreadCount } = storeToRefs(store)

// 启动自动更新
onMounted(() => store.startAutoUpdate())
onUnmounted(() => store.stopAutoUpdate())
</script>

<template>
  <div>
    <p>未读通知: {{ unreadCount }}</p>
    <div v-for="notification in notifications" :key="notification.event_id">
      {{ notification.event_name }}
    </div>
  </div>
</template>
```

### 2. 手动操作

```typescript
// 手动刷新
await store.fetchEventList()

// 标记已读
await store.markEventAsRead(eventId)

// 忽略事件
await store.ignoreEvent(eventId)

// 控制自动更新
store.startAutoUpdate()
store.stopAutoUpdate()
```

## 测试页面

访问 `/test-notifications` 可以查看和测试所有功能：
- 查看事件列表和状态
- 手动刷新数据
- 控制自动更新
- 测试标记已读和忽略功能

## 注意事项

1. **自动更新管理**: 确保在页面卸载时停止自动更新，避免内存泄漏
2. **错误处理**: 所有 API 调用都包含错误处理
3. **性能优化**: 使用 computed 属性进行数据计算，避免不必要的重复计算
4. **数据同步**: 操作后会自动重新获取最新数据，确保数据一致性

## 更新日志

- **2025-08-15**: 初始实现全局事件通知系统
  - 创建 notifications store
  - 更新 NavBarNotifications 组件
  - 更新网络事件页面
  - 添加测试页面和文档
